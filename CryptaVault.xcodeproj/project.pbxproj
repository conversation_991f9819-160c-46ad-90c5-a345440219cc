// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		240C120E2E33C2AC00051F70 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 240C120D2E33C2AC00051F70 /* AppDelegate.swift */; };
		240C12102E33C2AC00051F70 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 240C120F2E33C2AC00051F70 /* SceneDelegate.swift */; };
		240C12122E33C2AC00051F70 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 240C12112E33C2AC00051F70 /* ViewController.swift */; };
		240C12152E33C2AC00051F70 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 240C12132E33C2AC00051F70 /* Main.storyboard */; };
		240C12172E33C2AF00051F70 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 240C12162E33C2AF00051F70 /* Assets.xcassets */; };
		240C121A2E33C2AF00051F70 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 240C12182E33C2AF00051F70 /* LaunchScreen.storyboard */; };
		240C12252E33C2B000051F70 /* CryptaVaultTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 240C12242E33C2B000051F70 /* CryptaVaultTests.swift */; };
		240C122F2E33C2B000051F70 /* CryptaVaultUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 240C122E2E33C2B000051F70 /* CryptaVaultUITests.swift */; };
		240C12312E33C2B000051F70 /* CryptaVaultUITestsLaunchTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 240C12302E33C2B000051F70 /* CryptaVaultUITestsLaunchTests.swift */; };
		241D377E2E41048B00676B08 /* MainTabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241D377C2E41048B00676B08 /* MainTabBarController.swift */; };
		241D377F2E41048B00676B08 /* PasswordGeneratorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241D377D2E41048B00676B08 /* PasswordGeneratorViewController.swift */; };
		241D37842E41090B00676B08 /* OAuthManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241D37802E41090B00676B08 /* OAuthManager.swift */; };
		241D37852E41090B00676B08 /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241D37812E41090B00676B08 /* LoginViewController.swift */; };
		241D37862E41090B00676B08 /* APIExamples.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241D37822E41090B00676B08 /* APIExamples.swift */; };
		241D37872E41090B00676B08 /* APIManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 241D37832E41090B00676B08 /* APIManager.swift */; };
		24BBD7A62E4175E90004034D /* AccountViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24BBD7A52E4175E90004034D /* AccountViewController.swift */; };
		24BBD7AB2E4184920004034D /* CVWebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24BBD7A72E4184920004034D /* CVWebViewController.swift */; };
		24BBD7AC2E4184920004034D /* CVOAuthWebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24BBD7A82E4184920004034D /* CVOAuthWebViewController.swift */; };
		24BBD7AD2E4184920004034D /* PKCEManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24BBD7A92E4184920004034D /* PKCEManager.swift */; };
		24BBD7AE2E4184920004034D /* KeychainManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24BBD7AA2E4184920004034D /* KeychainManager.swift */; };
		52C6642A279942D9FBF478EB /* Pods_CryptaVaultTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9AE7BE25B1B952F30B71C79C /* Pods_CryptaVaultTests.framework */; };
		58E29A9D89D3A33AB86B18CE /* Pods_CryptaVault.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5AD2277D5EB28C72ECE11FCA /* Pods_CryptaVault.framework */; };
		6F6AC72FE1231F3828AFBFF2 /* Pods_CryptaVault_CryptaVaultUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 75B49AE4D5856EBA6F2E5267 /* Pods_CryptaVault_CryptaVaultUITests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		240C12212E33C2B000051F70 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 240C12022E33C2AC00051F70 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 240C12092E33C2AC00051F70;
			remoteInfo = CryptaVault;
		};
		240C122B2E33C2B000051F70 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 240C12022E33C2AC00051F70 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 240C12092E33C2AC00051F70;
			remoteInfo = CryptaVault;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1EA62A193CB1E05B1B50FE96 /* Pods-CryptaVault.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CryptaVault.release.xcconfig"; path = "Target Support Files/Pods-CryptaVault/Pods-CryptaVault.release.xcconfig"; sourceTree = "<group>"; };
		240C120A2E33C2AC00051F70 /* CryptaVault.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CryptaVault.app; sourceTree = BUILT_PRODUCTS_DIR; };
		240C120D2E33C2AC00051F70 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		240C120F2E33C2AC00051F70 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		240C12112E33C2AC00051F70 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		240C12142E33C2AC00051F70 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		240C12162E33C2AF00051F70 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		240C12192E33C2AF00051F70 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		240C121B2E33C2AF00051F70 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		240C12202E33C2B000051F70 /* CryptaVaultTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CryptaVaultTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		240C12242E33C2B000051F70 /* CryptaVaultTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptaVaultTests.swift; sourceTree = "<group>"; };
		240C122A2E33C2B000051F70 /* CryptaVaultUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = CryptaVaultUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		240C122E2E33C2B000051F70 /* CryptaVaultUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptaVaultUITests.swift; sourceTree = "<group>"; };
		240C12302E33C2B000051F70 /* CryptaVaultUITestsLaunchTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptaVaultUITestsLaunchTests.swift; sourceTree = "<group>"; };
		241D377C2E41048B00676B08 /* MainTabBarController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainTabBarController.swift; sourceTree = "<group>"; };
		241D377D2E41048B00676B08 /* PasswordGeneratorViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PasswordGeneratorViewController.swift; sourceTree = "<group>"; };
		241D37802E41090B00676B08 /* OAuthManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OAuthManager.swift; sourceTree = "<group>"; };
		241D37812E41090B00676B08 /* LoginViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		241D37822E41090B00676B08 /* APIExamples.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIExamples.swift; sourceTree = "<group>"; };
		241D37832E41090B00676B08 /* APIManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = APIManager.swift; sourceTree = "<group>"; };
		24BBD7A52E4175E90004034D /* AccountViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AccountViewController.swift; sourceTree = "<group>"; };
		24BBD7A72E4184920004034D /* CVWebViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CVWebViewController.swift; sourceTree = "<group>"; };
		24BBD7A82E4184920004034D /* CVOAuthWebViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CVOAuthWebViewController.swift; sourceTree = "<group>"; };
		24BBD7A92E4184920004034D /* PKCEManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PKCEManager.swift; sourceTree = "<group>"; };
		24BBD7AA2E4184920004034D /* KeychainManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = KeychainManager.swift; sourceTree = "<group>"; };
		3A43D97E79669C6D9476AABF /* Pods-CryptaVaultTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CryptaVaultTests.release.xcconfig"; path = "Target Support Files/Pods-CryptaVaultTests/Pods-CryptaVaultTests.release.xcconfig"; sourceTree = "<group>"; };
		3E3A70FD8E8F9D421005BA93 /* Pods-CryptaVaultTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CryptaVaultTests.debug.xcconfig"; path = "Target Support Files/Pods-CryptaVaultTests/Pods-CryptaVaultTests.debug.xcconfig"; sourceTree = "<group>"; };
		5AD2277D5EB28C72ECE11FCA /* Pods_CryptaVault.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_CryptaVault.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6658CFE90A62C555347D0B78 /* Pods-CryptaVault-CryptaVaultUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CryptaVault-CryptaVaultUITests.release.xcconfig"; path = "Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests.release.xcconfig"; sourceTree = "<group>"; };
		75B49AE4D5856EBA6F2E5267 /* Pods_CryptaVault_CryptaVaultUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_CryptaVault_CryptaVaultUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9AE7BE25B1B952F30B71C79C /* Pods_CryptaVaultTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_CryptaVaultTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C2B24F9EE909F9C6C593F27C /* Pods-CryptaVault.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CryptaVault.debug.xcconfig"; path = "Target Support Files/Pods-CryptaVault/Pods-CryptaVault.debug.xcconfig"; sourceTree = "<group>"; };
		FE4524945952AC3538250425 /* Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig"; path = "Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		240C12072E33C2AC00051F70 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				58E29A9D89D3A33AB86B18CE /* Pods_CryptaVault.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		240C121D2E33C2B000051F70 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				52C6642A279942D9FBF478EB /* Pods_CryptaVaultTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		240C12272E33C2B000051F70 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				6F6AC72FE1231F3828AFBFF2 /* Pods_CryptaVault_CryptaVaultUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		240C12012E33C2AC00051F70 = {
			isa = PBXGroup;
			children = (
				240C120C2E33C2AC00051F70 /* CryptaVault */,
				240C12232E33C2B000051F70 /* CryptaVaultTests */,
				240C122D2E33C2B000051F70 /* CryptaVaultUITests */,
				240C120B2E33C2AC00051F70 /* Products */,
				6DA1BA886BE936E9C4BA06E3 /* Pods */,
				44D4E7BAE77FBD7276649B02 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		240C120B2E33C2AC00051F70 /* Products */ = {
			isa = PBXGroup;
			children = (
				240C120A2E33C2AC00051F70 /* CryptaVault.app */,
				240C12202E33C2B000051F70 /* CryptaVaultTests.xctest */,
				240C122A2E33C2B000051F70 /* CryptaVaultUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		240C120C2E33C2AC00051F70 /* CryptaVault */ = {
			isa = PBXGroup;
			children = (
				24BBD7A82E4184920004034D /* CVOAuthWebViewController.swift */,
				24BBD7A72E4184920004034D /* CVWebViewController.swift */,
				24BBD7AA2E4184920004034D /* KeychainManager.swift */,
				24BBD7A92E4184920004034D /* PKCEManager.swift */,
				24BBD7A52E4175E90004034D /* AccountViewController.swift */,
				241D37822E41090B00676B08 /* APIExamples.swift */,
				241D37832E41090B00676B08 /* APIManager.swift */,
				241D37812E41090B00676B08 /* LoginViewController.swift */,
				241D37802E41090B00676B08 /* OAuthManager.swift */,
				241D377C2E41048B00676B08 /* MainTabBarController.swift */,
				241D377D2E41048B00676B08 /* PasswordGeneratorViewController.swift */,
				240C120D2E33C2AC00051F70 /* AppDelegate.swift */,
				240C120F2E33C2AC00051F70 /* SceneDelegate.swift */,
				240C12112E33C2AC00051F70 /* ViewController.swift */,
				240C12132E33C2AC00051F70 /* Main.storyboard */,
				240C12162E33C2AF00051F70 /* Assets.xcassets */,
				240C12182E33C2AF00051F70 /* LaunchScreen.storyboard */,
				240C121B2E33C2AF00051F70 /* Info.plist */,
			);
			path = CryptaVault;
			sourceTree = "<group>";
		};
		240C12232E33C2B000051F70 /* CryptaVaultTests */ = {
			isa = PBXGroup;
			children = (
				240C12242E33C2B000051F70 /* CryptaVaultTests.swift */,
			);
			path = CryptaVaultTests;
			sourceTree = "<group>";
		};
		240C122D2E33C2B000051F70 /* CryptaVaultUITests */ = {
			isa = PBXGroup;
			children = (
				240C122E2E33C2B000051F70 /* CryptaVaultUITests.swift */,
				240C12302E33C2B000051F70 /* CryptaVaultUITestsLaunchTests.swift */,
			);
			path = CryptaVaultUITests;
			sourceTree = "<group>";
		};
		44D4E7BAE77FBD7276649B02 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5AD2277D5EB28C72ECE11FCA /* Pods_CryptaVault.framework */,
				75B49AE4D5856EBA6F2E5267 /* Pods_CryptaVault_CryptaVaultUITests.framework */,
				9AE7BE25B1B952F30B71C79C /* Pods_CryptaVaultTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6DA1BA886BE936E9C4BA06E3 /* Pods */ = {
			isa = PBXGroup;
			children = (
				C2B24F9EE909F9C6C593F27C /* Pods-CryptaVault.debug.xcconfig */,
				1EA62A193CB1E05B1B50FE96 /* Pods-CryptaVault.release.xcconfig */,
				FE4524945952AC3538250425 /* Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig */,
				6658CFE90A62C555347D0B78 /* Pods-CryptaVault-CryptaVaultUITests.release.xcconfig */,
				3E3A70FD8E8F9D421005BA93 /* Pods-CryptaVaultTests.debug.xcconfig */,
				3A43D97E79669C6D9476AABF /* Pods-CryptaVaultTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		240C12092E33C2AC00051F70 /* CryptaVault */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 240C12342E33C2B000051F70 /* Build configuration list for PBXNativeTarget "CryptaVault" */;
			buildPhases = (
				AB1FB08EF1EF443002AE260C /* [CP] Check Pods Manifest.lock */,
				240C12062E33C2AC00051F70 /* Sources */,
				240C12072E33C2AC00051F70 /* Frameworks */,
				240C12082E33C2AC00051F70 /* Resources */,
				FC69E7EB35760FEFC86B310D /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CryptaVault;
			productName = CryptaVault;
			productReference = 240C120A2E33C2AC00051F70 /* CryptaVault.app */;
			productType = "com.apple.product-type.application";
		};
		240C121F2E33C2B000051F70 /* CryptaVaultTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 240C12372E33C2B000051F70 /* Build configuration list for PBXNativeTarget "CryptaVaultTests" */;
			buildPhases = (
				334E8457A3256FD074B419C2 /* [CP] Check Pods Manifest.lock */,
				240C121C2E33C2B000051F70 /* Sources */,
				240C121D2E33C2B000051F70 /* Frameworks */,
				240C121E2E33C2B000051F70 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				240C12222E33C2B000051F70 /* PBXTargetDependency */,
			);
			name = CryptaVaultTests;
			productName = CryptaVaultTests;
			productReference = 240C12202E33C2B000051F70 /* CryptaVaultTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		240C12292E33C2B000051F70 /* CryptaVaultUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 240C123A2E33C2B000051F70 /* Build configuration list for PBXNativeTarget "CryptaVaultUITests" */;
			buildPhases = (
				34B2385CA9FE1CCF133093D1 /* [CP] Check Pods Manifest.lock */,
				240C12262E33C2B000051F70 /* Sources */,
				240C12272E33C2B000051F70 /* Frameworks */,
				240C12282E33C2B000051F70 /* Resources */,
				607EAF85337B4F659D18DBD6 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				240C122C2E33C2B000051F70 /* PBXTargetDependency */,
			);
			name = CryptaVaultUITests;
			productName = CryptaVaultUITests;
			productReference = 240C122A2E33C2B000051F70 /* CryptaVaultUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		240C12022E33C2AC00051F70 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1420;
				LastUpgradeCheck = 1420;
				TargetAttributes = {
					240C12092E33C2AC00051F70 = {
						CreatedOnToolsVersion = 14.2;
					};
					240C121F2E33C2B000051F70 = {
						CreatedOnToolsVersion = 14.2;
						TestTargetID = 240C12092E33C2AC00051F70;
					};
					240C12292E33C2B000051F70 = {
						CreatedOnToolsVersion = 14.2;
						TestTargetID = 240C12092E33C2AC00051F70;
					};
				};
			};
			buildConfigurationList = 240C12052E33C2AC00051F70 /* Build configuration list for PBXProject "CryptaVault" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 240C12012E33C2AC00051F70;
			productRefGroup = 240C120B2E33C2AC00051F70 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				240C12092E33C2AC00051F70 /* CryptaVault */,
				240C121F2E33C2B000051F70 /* CryptaVaultTests */,
				240C12292E33C2B000051F70 /* CryptaVaultUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		240C12082E33C2AC00051F70 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				240C121A2E33C2AF00051F70 /* LaunchScreen.storyboard in Resources */,
				240C12172E33C2AF00051F70 /* Assets.xcassets in Resources */,
				240C12152E33C2AC00051F70 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		240C121E2E33C2B000051F70 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		240C12282E33C2B000051F70 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		334E8457A3256FD074B419C2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-CryptaVaultTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		34B2385CA9FE1CCF133093D1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-CryptaVault-CryptaVaultUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		607EAF85337B4F659D18DBD6 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-CryptaVault-CryptaVaultUITests/Pods-CryptaVault-CryptaVaultUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AB1FB08EF1EF443002AE260C /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-CryptaVault-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FC69E7EB35760FEFC86B310D /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CryptaVault/Pods-CryptaVault-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-CryptaVault/Pods-CryptaVault-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-CryptaVault/Pods-CryptaVault-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		240C12062E33C2AC00051F70 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				24BBD7AC2E4184920004034D /* CVOAuthWebViewController.swift in Sources */,
				241D37852E41090B00676B08 /* LoginViewController.swift in Sources */,
				240C12122E33C2AC00051F70 /* ViewController.swift in Sources */,
				241D37842E41090B00676B08 /* OAuthManager.swift in Sources */,
				241D37872E41090B00676B08 /* APIManager.swift in Sources */,
				240C120E2E33C2AC00051F70 /* AppDelegate.swift in Sources */,
				241D377F2E41048B00676B08 /* PasswordGeneratorViewController.swift in Sources */,
				24BBD7A62E4175E90004034D /* AccountViewController.swift in Sources */,
				24BBD7AD2E4184920004034D /* PKCEManager.swift in Sources */,
				24BBD7AE2E4184920004034D /* KeychainManager.swift in Sources */,
				241D37862E41090B00676B08 /* APIExamples.swift in Sources */,
				241D377E2E41048B00676B08 /* MainTabBarController.swift in Sources */,
				24BBD7AB2E4184920004034D /* CVWebViewController.swift in Sources */,
				240C12102E33C2AC00051F70 /* SceneDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		240C121C2E33C2B000051F70 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				240C12252E33C2B000051F70 /* CryptaVaultTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		240C12262E33C2B000051F70 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				240C12312E33C2B000051F70 /* CryptaVaultUITestsLaunchTests.swift in Sources */,
				240C122F2E33C2B000051F70 /* CryptaVaultUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		240C12222E33C2B000051F70 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 240C12092E33C2AC00051F70 /* CryptaVault */;
			targetProxy = 240C12212E33C2B000051F70 /* PBXContainerItemProxy */;
		};
		240C122C2E33C2B000051F70 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 240C12092E33C2AC00051F70 /* CryptaVault */;
			targetProxy = 240C122B2E33C2B000051F70 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		240C12132E33C2AC00051F70 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				240C12142E33C2AC00051F70 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		240C12182E33C2AF00051F70 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				240C12192E33C2AF00051F70 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		240C12322E33C2B000051F70 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		240C12332E33C2B000051F70 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		240C12352E33C2B000051F70 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C2B24F9EE909F9C6C593F27C /* Pods-CryptaVault.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CryptaVault/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.prodbits.apps.-cryptavault.CryptaVault";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		240C12362E33C2B000051F70 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1EA62A193CB1E05B1B50FE96 /* Pods-CryptaVault.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CryptaVault/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.prodbits.apps.-cryptavault.CryptaVault";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		240C12382E33C2B000051F70 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3E3A70FD8E8F9D421005BA93 /* Pods-CryptaVaultTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-cryptavault.CryptaVaultTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CryptaVault.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CryptaVault";
			};
			name = Debug;
		};
		240C12392E33C2B000051F70 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3A43D97E79669C6D9476AABF /* Pods-CryptaVaultTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-cryptavault.CryptaVaultTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/CryptaVault.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/CryptaVault";
			};
			name = Release;
		};
		240C123B2E33C2B000051F70 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = FE4524945952AC3538250425 /* Pods-CryptaVault-CryptaVaultUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-cryptavault.CryptaVaultUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CryptaVault;
			};
			name = Debug;
		};
		240C123C2E33C2B000051F70 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6658CFE90A62C555347D0B78 /* Pods-CryptaVault-CryptaVaultUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 375GT6S3QN;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.dehengxu.apps.-cryptavault.CryptaVaultUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = CryptaVault;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		240C12052E33C2AC00051F70 /* Build configuration list for PBXProject "CryptaVault" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				240C12322E33C2B000051F70 /* Debug */,
				240C12332E33C2B000051F70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		240C12342E33C2B000051F70 /* Build configuration list for PBXNativeTarget "CryptaVault" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				240C12352E33C2B000051F70 /* Debug */,
				240C12362E33C2B000051F70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		240C12372E33C2B000051F70 /* Build configuration list for PBXNativeTarget "CryptaVaultTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				240C12382E33C2B000051F70 /* Debug */,
				240C12392E33C2B000051F70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		240C123A2E33C2B000051F70 /* Build configuration list for PBXNativeTarget "CryptaVaultUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				240C123B2E33C2B000051F70 /* Debug */,
				240C123C2E33C2B000051F70 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 240C12022E33C2AC00051F70 /* Project object */;
}
