#!/bin/bash

# OAuth Callback 服务启动脚本

set -e

echo "Starting OAuth Callback Server..."

# 检查是否存在 .env 文件
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "No .env file found, using default configuration"
fi

# 设置默认值
export PORT=${PORT:-3000}
export ENV=${ENV:-development}
export APP_SCHEME=${APP_SCHEME:-cryptaauth}

echo "Configuration:"
echo "- Port: $PORT"
echo "- Environment: $ENV"
echo "- App Scheme: $APP_SCHEME"

# 编译并运行
echo "Building and starting server..."
go build -o oauth-callback-server main.go
./oauth-callback-server