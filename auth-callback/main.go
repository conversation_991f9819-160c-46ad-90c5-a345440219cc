package main

import (
	"log"
	"net/http"
	"net/url"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Config 配置结构
type Config struct {
	Port        string
	AppScheme   string
	Environment string
}

// 获取配置
func getConfig() *Config {
	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}

	appScheme := os.Getenv("APP_SCHEME")
	if appScheme == "" {
		appScheme = "cryptavault"
	}

	env := os.Getenv("ENV")
	if env == "" {
		env = "development"
	}

	return &Config{
		Port:        port,
		AppScheme:   appScheme,
		Environment: env,
	}
}

// 处理 OAuth callback
func handleOAuthCallback(c *gin.Context) {
	// 获取查询参数
	code := c.Query("code")
	state := c.Query("state")
	error := c.Query("error")
	provider := c.<PERSON><PERSON>("provider")

	// 记录请求日志
	log.Printf("OAuth callback received for provider: %s", provider)
	log.Printf("Parameters - code: %s, state: %s, error: %s", 
		maskSensitiveData(code), state, error)

	// 检查是否有错误
	if error != "" {
		log.Printf("OAuth error received: %s", error)
		errorDescription := c.Query("error_description")
		
		// 构建错误重定向 URL
		redirectURL := buildAppRedirectURL(provider, map[string]string{
			"error":             error,
			"error_description": errorDescription,
		})
		
		c.Redirect(http.StatusFound, redirectURL)
		return
	}

	// 验证必需参数
	if code == "" {
		log.Println("Missing authorization code")
		redirectURL := buildAppRedirectURL(provider, map[string]string{
			"error": "missing_code",
		})
		c.Redirect(http.StatusFound, redirectURL)
		return
	}

	if state == "" {
		log.Println("Missing state parameter")
		redirectURL := buildAppRedirectURL(provider, map[string]string{
			"error": "missing_state",
		})
		c.Redirect(http.StatusFound, redirectURL)
		return
	}

	// 构建成功重定向 URL
	params := map[string]string{
		"code":  code,
		"state": state,
	}

	// 添加其他可能的查询参数
	if scope := c.Query("scope"); scope != "" {
		params["scope"] = scope
	}

	redirectURL := buildAppRedirectURL(provider, params)
	
	log.Printf("Redirecting to app: %s", redirectURL)
	c.Redirect(http.StatusFound, redirectURL)
}

// 构建应用重定向 URL
func buildAppRedirectURL(provider string, params map[string]string) string {
	config := getConfig()
	
	// 构建基础 URL: cryptaauth://oauth/google
	baseURL := config.AppScheme + "://oauth/" + provider
	
	// 添加查询参数
	if len(params) > 0 {
		values := url.Values{}
		for key, value := range params {
			if value != "" {
				values.Add(key, value)
			}
		}
		if len(values) > 0 {
			baseURL += "?" + values.Encode()
		}
	}
	
	return baseURL
}

// 脱敏敏感数据用于日志记录
func maskSensitiveData(data string) string {
	if len(data) <= 8 {
		return "***"
	}
	return data[:4] + "***" + data[len(data)-4:]
}

// 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "ok",
		"service":   "oauth-callback",
		"version":   "1.0.0",
		"timestamp": gin.H{},
	})
}

func main() {
	config := getConfig()
	
	// 设置 Gin 模式
	if config.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建 Gin 实例
	r := gin.Default()

	// CORS 配置
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowAllOrigins = true
	corsConfig.AllowMethods = []string{"GET", "POST", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type"}
	r.Use(cors.New(corsConfig))

	// 中间件：请求日志
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 路由
	r.GET("/health", healthCheck)
	r.GET("/auth/callback", func(c *gin.Context) {
		// 默认处理 Google OAuth
		c.Params = append(c.Params, gin.Param{Key: "provider", Value: "google"})
		handleOAuthCallback(c)
	})
	r.GET("/auth/callback/:provider", handleOAuthCallback)

	// 启动服务器
	log.Printf("Starting OAuth callback server on port %s", config.Port)
	log.Printf("App scheme: %s", config.AppScheme)
	log.Printf("Environment: %s", config.Environment)
	
	if err := r.Run(":" + config.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}