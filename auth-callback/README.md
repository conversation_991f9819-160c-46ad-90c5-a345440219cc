# OAuth Callback Service

OAuth 回调服务，用于处理第三方 OAuth 授权回调并重定向到 iOS 应用。

## 功能特性

- 接收 OAuth callback 请求
- 参数验证和错误处理
- 302 重定向到 iOS 应用深度链接
- 支持多个 OAuth 提供商（Google、Facebook、Twitter）
- 完整的日志记录
- 健康检查接口

## 快速开始

### 1. 环境要求

- Go 1.19 或更高版本
- macOS/Linux/Windows

### 2. 安装依赖

```bash
go mod download
```

### 3. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```bash
PORT=3000
ENV=development
APP_SCHEME=cryptavault
```

### 4. 启动服务

使用启动脚本：
```bash
./start.sh
```

或者直接运行：
```bash
go run main.go
```

## API 接口

### OAuth Callback

**接口路径:** `GET /auth/callback` 或 `GET /auth/callback/:provider`

**参数说明:**
- `code` - OAuth 授权码（必需）
- `state` - OAuth 状态参数（必需）
- `error` - 错误信息（可选）
- `error_description` - 错误描述（可选）
- `scope` - 授权范围（可选）

**成功响应:**
- HTTP 302 重定向到 `cryptavault://oauth/:provider?code=xxx&state=yyy`

**错误响应:**
- HTTP 302 重定向到 `cryptavault://oauth/:provider?error=xxx`

### 健康检查

**接口路径:** `GET /health`

**响应示例:**
```json
{
  "status": "ok",
  "service": "oauth-callback",
  "version": "1.0.0",
  "timestamp": {}
}
```

## 使用场景

1. **OAuth 授权流程**
   ```
   iOS App -> OAuth 提供商 -> Callback Service -> iOS App
   ```

2. **支持的 OAuth 提供商**
   - Google OAuth 2.0
   - Facebook Login
   - Twitter OAuth 2.0

## 配置说明

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| PORT | 3000 | 服务端口 |
| ENV | development | 运行环境 |
| APP_SCHEME | cryptavault | iOS 应用 URL Scheme |

## 安全特性

- 参数验证
- 敏感数据脱敏日志
- CORS 跨域支持
- 错误处理机制

## 开发

### 项目结构
```
auth-callback/
├── main.go              # 主程序
├── go.mod              # Go 模块文件
├── go.sum              # 依赖版本锁定
├── .env.example        # 环境变量模板
├── start.sh            # 启动脚本
└── README.md           # 项目文档
```

### 调试模式

设置环境变量 `ENV=development` 启用详细日志。

## 部署

### 编译

```bash
go build -o oauth-callback-server main.go
```

### 运行

```bash
./oauth-callback-server
```

## 故障排除

1. **端口被占用**
   - 修改 `PORT` 环境变量
   - 检查是否有其他服务占用端口

2. **权限错误**
   - 确保启动脚本有执行权限：`chmod +x start.sh`

3. **依赖问题**
   - 重新安装依赖：`go mod download`
   - 清理缓存：`go clean -cache`

## 许可证

MIT License