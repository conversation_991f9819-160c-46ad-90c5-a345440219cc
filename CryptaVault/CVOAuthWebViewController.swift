\n//\n//  CVOAuthWebViewController.swift\n//  CryptaVault\n//\n//  Created by <PERSON><PERSON><PERSON> on 2025/8/5.\n//\n\nimport UIKit\nimport WebKit\n\n/// OAuth 特定错误类型\nenum CVOAuthError: Error, LocalizedError {\n    case authorizationDenied\n    case invalidState\n    case missingAuthorizationCode\n    case urlInterceptionFailed\n    case userCancelled\n    case timeout\n    case unknownError(String)\n    \n    var errorDescription: String? {\n        switch self {\n        case .authorizationDenied:\n            return \"用户拒绝了授权请求\"\n        case .invalidState:\n            return \"OAuth 状态参数无效\"\n        case .missingAuthorizationCode:\n            return \"缺少授权码\"\n        case .urlInterceptionFailed:\n            return \"URL 拦截失败\"\n        case .userCancelled:\n            return \"用户取消了操作\"\n        case .timeout:\n            return \"操作超时\"\n        case .unknownError(let message):\n            return \"未知错误: \\(message)\"\n        }\n    }\n}\n\n/// OAuth 流程状态\nenum CVOAuthState {\n    case idle\n    case loading\n    case authorizing\n    case success(authorizationCode: String, state: String)\n    case failed(CVOAuthError)\n    case cancelled\n}\n\n/// OAuth 结果\nenum CVOAuthResult {\n    case success(authorizationCode: String, state: String)\n    case failure(CVOAuthError)\n    case cancelled\n}\n\n/// OAuth WebView 代理协议\nprotocol CVOAuthWebViewControllerDelegate: AnyObject {\n    func oauthWebViewController(_ controller: CVOAuthWebViewController, didCompleteWithResult result: CVOAuthResult)\n    func oauthWebViewController(_ controller: CVOAuthWebViewController, didChangeState state: CVOAuthState)\n}\n\n/// OAuth 专用 WebView 控制器\nclass CVOAuthWebViewController: CVWebViewController {\n    \n    // MARK: - Properties\n    \n    weak var oauthDelegate: CVOAuthWebViewControllerDelegate?\n    private let provider: OAuthProvider\n    private let expectedCallbackScheme: String\n    private var expectedState: String?\n    \n    private(set) var oauthState: CVOAuthState = .idle {\n        didSet {\n            DispatchQueue.main.async {\n                self.oauthDelegate?.oauthWebViewController(self, didChangeState: self.oauthState)\n            }\n        }\n    }\n    \n    private var timeoutTimer: Timer?\n    var timeoutInterval: TimeInterval = 60.0\n    var allowsManualCancellation: Bool = true\n    \n    // MARK: - UI Components\n    \n    private lazy var titleLabel: UILabel = {\n        let label = UILabel()\n        label.font = UIFont.systemFont(ofSize: 17, weight: .semibold)\n        label.textAlignment = .center\n        label.textColor = .label\n        return label\n    }()\n    \n    private lazy var cancelButton: UIBarButtonItem = {\n        UIBarButtonItem(title: \"取消\", style: .plain, target: self, action: #selector(cancelOAuth))\n    }()\n    \n    // MARK: - Initialization\n    \n    init(provider: OAuthProvider, callbackScheme: String = \"cryptavault\") {\n        self.provider = provider\n        self.expectedCallbackScheme = callbackScheme\n        \n        super.init(nibName: nil, bundle: nil)\n        \n        showsNavigationToolbar = false\n        showsProgressView = true\n        allowsZoom = false\n        delegate = self\n        \n        configureSecureWebView()\n    }\n    \n    required init?(coder: NSCoder) {\n        fatalError(\"init(coder:) has not been implemented\")\n    }\n    \n    // MARK: - Lifecycle\n    \n    override func viewDidLoad() {\n        super.viewDidLoad()\n        setupOAuthUI()\n        setupTimeoutTimer()\n    }\n    \n    override func viewWillDisappear(_ animated: Bool) {\n        super.viewWillDisappear(animated)\n        \n        if oauthState == .loading || oauthState == .authorizing {\n            handleOAuthResult(.cancelled)\n        }\n    }\n    \n    deinit {\n        timeoutTimer?.invalidate()\n    }\n    \n    // MARK: - Setup Methods\n    \n    private func setupOAuthUI() {\n        titleLabel.text = \"\\(provider.rawValue.capitalized) 登录\"\n        navigationItem.titleView = titleLabel\n        \n        if allowsManualCancellation {\n            navigationItem.leftBarButtonItem = cancelButton\n        }\n        \n        modalPresentationStyle = .pageSheet\n        \n        if #available(iOS 15.0, *) {\n            if let sheet = sheetPresentationController {\n                sheet.detents = [.large()]\n                sheet.prefersGrabberVisible = true\n            }\n        }\n    }\n    \n    private func configureSecureWebView() {\n        let dataStore = WKWebsiteDataStore.nonPersistent()\n        webView.configuration.websiteDataStore = dataStore\n        customUserAgent = \"CryptaVault-iOS/1.0 (OAuth Client)\"\n        webView.scrollView.isScrollEnabled = true\n        webView.scrollView.bounces = false\n        webView.configuration.preferences.javaScriptCanOpenWindowsAutomatically = false\n    }\n    \n    private func setupTimeoutTimer() {\n        timeoutTimer?.invalidate()\n        timeoutTimer = Timer.scheduledTimer(withTimeInterval: timeoutInterval, repeats: false) { [weak self] _ in\n            self?.handleTimeout()\n        }\n    }\n    \n    // MARK: - Public Methods\n    \n    func startOAuthFlow(authorizationURL: URL, state: String) {\n        expectedState = state\n        oauthState = .loading\n        load(url: authorizationURL)\n    }\n    \n    // MARK: - Private Methods\n    \n    private func handleTimeout() {\n        handleOAuthResult(.failure(.timeout))\n    }\n    \n    private func handleOAuthResult(_ result: CVOAuthResult) {\n        timeoutTimer?.invalidate()\n        oauthState = {\n            switch result {\n            case .success(let code, let state):\n                return .
success(authorizationCode: code, state: state)
            case .failure(let error):
                return .failed(error)
            case .cancelled:
                return .cancelled
            }
        }()
        
        DispatchQueue.main.async {
            self.oauthDelegate?.oauthWebViewController(self, didCompleteWithResult: result)
        }
    }
    
    private func parseCallbackURL(_ url: URL) -> CVOAuthResult {
        guard url.scheme?.lowercased() == expectedCallbackScheme.lowercased() else {
            return .failure(.urlInterceptionFailed)
        }
        
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false) else {
            return .failure(.urlInterceptionFailed)
        }
        
        // 检查错误参数
        if let errorParam = components.queryItems?.first(where: { $0.name == "error" })?.value {
            if errorParam == "access_denied" {
                return .failure(.authorizationDenied)
            } else {
                return .failure(.unknownError(errorParam))
            }
        }
        
        // 获取授权码
        guard let authorizationCode = components.queryItems?.first(where: { $0.name == "code" })?.value else {
            return .failure(.missingAuthorizationCode)
        }
        
        // 验证状态参数
        let receivedState = components.queryItems?.first(where: { $0.name == "state" })?.value
        guard let expectedState = expectedState, receivedState == expectedState else {
            return .failure(.invalidState)
        }
        
        return .success(authorizationCode: authorizationCode, state: receivedState ?? "")
    }
    
    @objc private func cancelOAuth() {
        handleOAuthResult(.cancelled)
    }
}

// MARK: - CVWebViewControllerDelegate
extension CVOAuthWebViewController: CVWebViewControllerDelegate {
    
    func webViewController(_ controller: CVWebViewController, didStartLoading url: URL?) {
        oauthState = .loading
    }
    
    func webViewController(_ controller: CVWebViewController, didFinishLoading url: URL?) {
        oauthState = .authorizing
    }
    
    func webViewController(_ controller: CVWebViewController, didFailLoading error: CVWebViewError) {
        let oauthError: CVOAuthError = .unknownError(error.localizedDescription)
        handleOAuthResult(.failure(oauthError))
    }
    
    func webViewController(_ controller: CVWebViewController, decidePolicyFor navigationAction: WKNavigationAction) -> WKNavigationActionPolicy {
        guard let url = navigationAction.request.url else {
            return .allow
        }
        
        // 检查是否是回调 URL
        if url.scheme?.lowercased() == expectedCallbackScheme.lowercased() {
            let result = parseCallbackURL(url)
            handleOAuthResult(result)
            return .cancel
        }
        
        return .allow
    }
}