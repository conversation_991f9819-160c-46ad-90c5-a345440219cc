//
//  KeychainManager.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/5.
//

import Foundation
import Security

/// Keychain 错误类型
enum KeychainError: Error, LocalizedError {
    case duplicateItem
    case itemNotFound
    case invalidItemFormat
    case unexpectedStatus(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .duplicateItem:
            return "Keychain item already exists"
        case .itemNotFound:
            return "Keychain item not found"
        case .invalidItemFormat:
            return "Invalid keychain item format"
        case .unexpectedStatus(let status):
            return "Unexpected keychain status: \(status)"
        }
    }
}

/// Keychain 管理器 - 用于安全存储敏感数据
class KeychainManager {
    static let shared = KeychainManager()
    
    private let serviceName = "com.cryptavault.oauth"
    
    private init() {}
    
    // MARK: - Generic Methods
    
    /// 存储数据到 Keychain
    /// - Parameters:
    ///   - data: 要存储的数据
    ///   - key: 存储键
    /// - Throws: KeychainError
    func store(data: Data, forKey key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        switch status {
        case errSecSuccess:
            break
        case errSecDuplicateItem:
            // 如果已存在，则更新
            try update(data: data, forKey: key)
        default:
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// 从 Keychain 读取数据
    /// - Parameter key: 存储键
    /// - Returns: 数据
    /// - Throws: KeychainError
    func retrieve(forKey key: String) throws -> Data {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        switch status {
        case errSecSuccess:
            guard let data = result as? Data else {
                throw KeychainError.invalidItemFormat
            }
            return data
        case errSecItemNotFound:
            throw KeychainError.itemNotFound
        default:
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// 更新 Keychain 中的数据
    /// - Parameters:
    ///   - data: 新数据
    ///   - key: 存储键
    /// - Throws: KeychainError
    private func update(data: Data, forKey key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key
        ]
        
        let attributes: [String: Any] = [
            kSecValueData as String: data
        ]
        
        let status = SecItemUpdate(query as CFDictionary, attributes as CFDictionary)
        
        guard status == errSecSuccess else {
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// 删除 Keychain 中的数据
    /// - Parameter key: 存储键
    /// - Throws: KeychainError
    func delete(forKey key: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        switch status {
        case errSecSuccess, errSecItemNotFound:
            break
        default:
            throw KeychainError.unexpectedStatus(status)
        }
    }
    
    /// 检查 Keychain 中是否存在指定键的数据
    /// - Parameter key: 存储键
    /// - Returns: 是否存在
    func exists(forKey key: String) -> Bool {
        do {
            _ = try retrieve(forKey: key)
            return true
        } catch {
            return false
        }
    }
    
    // MARK: - String Convenience Methods
    
    /// 存储字符串到 Keychain
    /// - Parameters:
    ///   - string: 要存储的字符串
    ///   - key: 存储键
    /// - Throws: KeychainError
    func store(string: String, forKey key: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.invalidItemFormat
        }
        try store(data: data, forKey: key)
    }
    
    /// 从 Keychain 读取字符串
    /// - Parameter key: 存储键
    /// - Returns: 字符串
    /// - Throws: KeychainError
    func retrieveString(forKey key: String) throws -> String {
        let data = try retrieve(forKey: key)
        guard let string = String(data: data, encoding: .utf8) else {
            throw KeychainError.invalidItemFormat
        }
        return string
    }
    
    // MARK: - OAuth Specific Methods
    
    /// OAuth 存储键常量
    struct OAuthKeys {
        static let accessToken = "oauth_access_token"
        static let refreshToken = "oauth_refresh_token"
        static let provider = "oauth_provider"
        static let codeVerifier = "oauth_code_verifier"
        static let state = "oauth_state"
    }
    
    /// 存储 OAuth 访问令牌
    /// - Parameter token: 访问令牌
    /// - Throws: KeychainError
    func storeAccessToken(_ token: String) throws {
        try store(string: token, forKey: OAuthKeys.accessToken)
    }
    
    /// 读取 OAuth 访问令牌
    /// - Returns: 访问令牌
    /// - Throws: KeychainError
    func getAccessToken() throws -> String {
        return try retrieveString(forKey: OAuthKeys.accessToken)
    }
    
    /// 存储 OAuth 刷新令牌
    /// - Parameter token: 刷新令牌
    /// - Throws: KeychainError
    func storeRefreshToken(_ token: String) throws {
        try store(string: token, forKey: OAuthKeys.refreshToken)
    }
    
    /// 读取 OAuth 刷新令牌
    /// - Returns: 刷新令牌
    /// - Throws: KeychainError
    func getRefreshToken() throws -> String {
        return try retrieveString(forKey: OAuthKeys.refreshToken)
    }
    
    /// 存储 OAuth 提供商
    /// - Parameter provider: 提供商名称
    /// - Throws: KeychainError
    func storeProvider(_ provider: String) throws {
        try store(string: provider, forKey: OAuthKeys.provider)
    }
    
    /// 读取 OAuth 提供商
    /// - Returns: 提供商名称
    /// - Throws: KeychainError
    func getProvider() throws -> String {
        return try retrieveString(forKey: OAuthKeys.provider)
    }
    
    /// 存储 PKCE Code Verifier
    /// - Parameter codeVerifier: Code Verifier
    /// - Throws: KeychainError
    func storeCodeVerifier(_ codeVerifier: String) throws {
        try store(string: codeVerifier, forKey: OAuthKeys.codeVerifier)
    }
    
    /// 读取 PKCE Code Verifier
    /// - Returns: Code Verifier
    /// - Throws: KeychainError
    func getCodeVerifier() throws -> String {
        return try retrieveString(forKey: OAuthKeys.codeVerifier)
    }
    
    /// 存储 OAuth State
    /// - Parameter state: State 参数
    /// - Throws: KeychainError
    func storeState(_ state: String) throws {
        try store(string: state, forKey: OAuthKeys.state)
    }
    
    /// 读取 OAuth State
    /// - Returns: State 参数
    /// - Throws: KeychainError
    func getState() throws -> String {
        return try retrieveString(forKey: OAuthKeys.state)
    }
    
    /// 清除所有 OAuth 相关数据
    func clearOAuthData() {
        let keys = [
            OAuthKeys.accessToken,
            OAuthKeys.refreshToken,
            OAuthKeys.provider,
            OAuthKeys.codeVerifier,
            OAuthKeys.state
        ]
        
        for key in keys {
            try? delete(forKey: key)
        }
    }
    
    /// 检查是否已登录（存在有效的访问令牌）
    /// - Returns: 是否已登录
    func isLoggedIn() -> Bool {
        return exists(forKey: OAuthKeys.accessToken)
    }
}

// MARK: - 扩展支持 Codable 对象
extension KeychainManager {
    
    /// 存储 Codable 对象到 Keychain
    /// - Parameters:
    ///   - object: 要存储的对象
    ///   - key: 存储键
    /// - Throws: KeychainError
    func store<T: Codable>(object: T, forKey key: String) throws {
        let encoder = JSONEncoder()
        let data = try encoder.encode(object)
        try store(data: data, forKey: key)
    }
    
    /// 从 Keychain 读取 Codable 对象
    /// - Parameters:
    ///   - type: 对象类型
    ///   - key: 存储键
    /// - Returns: 对象实例
    /// - Throws: KeychainError
    func retrieve<T: Codable>(objectOfType type: T.Type, forKey key: String) throws -> T {
        let data = try retrieve(forKey: key)
        let decoder = JSONDecoder()
        return try decoder.decode(type, from: data)
    }
}