//
//  OAuthManager.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/4.
//

import Foundation
import UIKit
import WebKit

// MARK: - OAuth Token
struct OAuthToken: Codable {
    let accessToken: String
    let refreshToken: String?
    let expiresIn: TimeInterval
    let tokenType: String
    let scope: String?
    
    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case expiresIn = "expires_in"
        case tokenType = "token_type"
        case scope
    }
}

// MARK: - OAuth Provider
enum OAuthProvider: String, CaseIterable {
    case google = "google"
    case facebook = "facebook"
    case twitter = "twitter"
    
    var clientId: String {
        switch self {
        case .google:
            return "369881809855-9ip4fejrvqgc8ho644duhilcu4k5cl36.apps.googleusercontent.com"
        case .facebook:
            return "YOUR_FACEBOOK_CLIENT_ID"
        case .twitter:
            return "YOUR_TWITTER_CLIENT_ID"
        }
    }
    
    var accessToken: String {
        switch self {
        case .google:
            return "CkO-V7OP0ASqqhxc4KoCz7vc"
        case .facebook:
            return ""
        case .twitter:
            return ""
        }
    }
    
    var redirectURI: String {
        return "cryptavault://auth-callback"
    }
    
    var authorizationURL: String {
        switch self {
        case .google:
            return "https://accounts.google.com/o/oauth2/v2/auth"
        case .facebook:
            return "https://www.facebook.com/v18.0/dialog/oauth"
        case .twitter:
            return "https://twitter.com/i/oauth2/authorize"
        }
    }
    
    var tokenURL: String {
        switch self {
        case .google:
            return "https://oauth2.googleapis.com/token"
        case .facebook:
            return "https://graph.facebook.com/v18.0/oauth/access_token"
        case .twitter:
            return "https://api.twitter.com/2/oauth2/token"
        }
    }
    
    var scope: String {
        switch self {
        case .google:
            return "openid email profile"
        case .facebook:
            return "email public_profile"
        case .twitter:
            return "tweet.read users.read"
        }
    }
}

// MARK: - OAuth Error
enum OAuthError: Error, LocalizedError {
    case invalidURL
    case authorizationDenied
    case invalidState
    case missingAuthorizationCode
    case tokenExchangeFailed(String)
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid OAuth URL"
        case .authorizationDenied:
            return "User denied authorization"
        case .invalidState:
            return "Invalid OAuth state parameter"
        case .missingAuthorizationCode:
            return "Missing authorization code"
        case .tokenExchangeFailed(let message):
            return "Token exchange failed: \(message)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - OAuth Request Models
struct OAuthTokenRequest: APIRequest {
    typealias Response = OAuthToken
    
    let path: String
    let method: HTTPMethod = .POST
    let parameters: [String: Any]?
    let headers: [String: String]?
    
    init(provider: OAuthProvider, authorizationCode: String, codeVerifier: String? = nil) {
        self.path = ""
        
        var params: [String: Any] = [
            "grant_type": "authorization_code",
            "client_id": provider.clientId,
            "redirect_uri": provider.redirectURI,
            "code": authorizationCode
        ]
        
        if let codeVerifier = codeVerifier {
            params["code_verifier"] = codeVerifier
        }
        
        self.parameters = params
        self.headers = ["Content-Type": "application/x-www-form-urlencoded"]
    }
}

// MARK: - OAuth Manager
class OAuthManager {
    static let shared = OAuthManager()
    
    private var currentAuthSession: (provider: OAuthProvider, state: String, completion: (Result<OAuthToken, Error>) -> Void)?
    private let apiManager: APIManager
    private var currentOAuthController: CVOAuthWebViewController?
    
    private init() {
        self.apiManager = APIManager.shared
    }
    
    // MARK: - OAuth Login Methods
    func loginWithGoogle(completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        startOAuthFlow(provider: .google, completion: completion)
    }
    
    func loginWithFacebook(completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        startOAuthFlow(provider: .facebook, completion: completion)
    }
    
    func loginWithTwitter(completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        startOAuthFlow(provider: .twitter, completion: completion)
    }
    
    // MARK: - Logout
    func logout(completion: @escaping (Bool) -> Void) {
        // Clear stored tokens
        UserDefaults.standard.removeObject(forKey: "oauth_token")
        UserDefaults.standard.removeObject(forKey: "oauth_refresh_token")
        UserDefaults.standard.removeObject(forKey: "oauth_provider")
        
        // Clear session
        currentAuthSession = nil
        
        completion(true)
    }
    
    // MARK: - Private Methods
    private func startOAuthFlow(provider: OAuthProvider, completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        let state = generateRandomState()
        currentAuthSession = (provider: provider, state: state, completion: completion)
        
        do {
            // 生成并存储 PKCE 参数
            let pkceParams = try PKCEManager.shared.generateAndStorePKCEParams()
            
            // 构建包含 PKCE 的授权 URL
            guard let authURL = buildAuthorizationURL(provider: provider, state: state, codeChallenge: pkceParams.challenge, codeChallengeMethod: pkceParams.method) else {
                completion(.failure(OAuthError.invalidURL))
                return
            }
            
            // 安全存储状态参数
            try KeychainManager.shared.storeState(state)
            
            // 使用 WKWebView 打开 OAuth URL
            DispatchQueue.main.async {
                self.presentOAuthWebView(provider: provider, authURL: authURL, state: state, completion: completion)
            }
            
        } catch {
            completion(.failure(OAuthError.networkError(error)))
        }
    }
    
    private func buildAuthorizationURL(provider: OAuthProvider, state: String, codeChallenge: String, codeChallengeMethod: PKCEManager.CodeChallengeMethod) -> URL? {
        var components = URLComponents(string: provider.authorizationURL)
        components?.queryItems = [
            URLQueryItem(name: "client_id", value: provider.clientId),
            URLQueryItem(name: "redirect_uri", value: provider.redirectURI),
            URLQueryItem(name: "response_type", value: "code"),
            URLQueryItem(name: "scope", value: provider.scope),
            URLQueryItem(name: "state", value: state),
            URLQueryItem(name: "code_challenge", value: codeChallenge),
            URLQueryItem(name: "code_challenge_method", value: codeChallengeMethod.rawValue)
        ]
        
        return components?.url
    }
    
    private func generateRandomState() -> String {
        let characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        return String((0..<32).map { _ in characters.randomElement()! })
    }
    
    // MARK: - Handle OAuth Callback
    func handleOAuthCallback(url: URL) {
        // 这个方法现在由 CVOAuthWebViewController 处理，保留以兼容现有代码
        // 实际的回调处理在 presentOAuthWebView 的完成回调中进行
    }
    
    private func exchangeCodeForToken(provider: OAuthProvider, authorizationCode: String, completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        do {
            // 获取存储的 PKCE Code Verifier
            let codeVerifier = try PKCEManager.shared.getAndClearCodeVerifier()
            
            // Create a custom API manager for token endpoint
            let tokenAPIManager = apiManager.updateConfiguration(baseURL: provider.tokenURL)
            
            let request = OAuthTokenRequest(provider: provider, authorizationCode: authorizationCode, codeVerifier: codeVerifier)
            
            tokenAPIManager.request(request) { result in
                switch result {
                case .success(let token):
                    self.storeTokenSecurely(token, provider: provider)
                    completion(.success(token))
                case .failure(let error):
                    completion(.failure(error))
                }
            }
        } catch {
            completion(.failure(OAuthError.networkError(error)))
        }
    }
    
}

    
    // MARK: - WebView OAuth Methods
    
    /// 展示 OAuth WebView 控制器
    private func presentOAuthWebView(provider: OAuthProvider, authURL: URL, state: String, completion: @escaping (Result<OAuthToken, Error>) -> Void) {
        guard let topViewController = getTopViewController() else {
            completion(.failure(OAuthError.invalidURL))
            return
        }
        
        let oauthController = CVOAuthWebViewController(provider: provider)
        let navigationController = UINavigationController(rootViewController: oauthController)
        
        // 设置 OAuth 代理
        oauthController.oauthDelegate = self
        currentOAuthController = oauthController
        
        // 启动 OAuth 流程
        oauthController.startOAuthFlow(authorizationURL: authURL, state: state)
        
        // 展示控制器
        topViewController.present(navigationController, animated: true)
    }
    
    /// 获取顶层视图控制器
    private func getTopViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return nil
        }
        
        var topController = window.rootViewController
        while let presentedController = topController?.presentedViewController {
            topController = presentedController
        }
        
        return topController
    }
    
    // MARK: - Updated Token Management
    
    private func storeTokenSecurely(_ token: OAuthToken, provider: OAuthProvider) {
        do {
            // 使用 Keychain 安全存储令牌
            try KeychainManager.shared.storeAccessToken(token.accessToken)
            if let refreshToken = token.refreshToken {
                try KeychainManager.shared.storeRefreshToken(refreshToken)
            }
            try KeychainManager.shared.storeProvider(provider.rawValue)
        } catch {
            print("Failed to store token securely: \(error)")
            // 降级到 UserDefaults 作为备用方案
            UserDefaults.standard.set(token.accessToken, forKey: "oauth_token")
            if let refreshToken = token.refreshToken {
                UserDefaults.standard.set(refreshToken, forKey: "oauth_refresh_token")
            }
            UserDefaults.standard.set(provider.rawValue, forKey: "oauth_provider")
        }
    }
    
    // MARK: - Updated Token Retrieval
    
    /// 获取存储的令牌（优先从 Keychain 读取）
    func getStoredToken() -> OAuthToken? {
        // 优先尝试从 Keychain 读取
        do {
            let accessToken = try KeychainManager.shared.getAccessToken()
            let refreshToken = try? KeychainManager.shared.getRefreshToken()
            
            return OAuthToken(
                accessToken: accessToken,
                refreshToken: refreshToken,
                expiresIn: 3600, // Default value
                tokenType: "Bearer",
                scope: nil
            )
        } catch {
            // 降级到 UserDefaults
            guard let accessToken = UserDefaults.standard.string(forKey: "oauth_token") else {
                return nil
            }
            
            let refreshToken = UserDefaults.standard.string(forKey: "oauth_refresh_token")
            
            return OAuthToken(
                accessToken: accessToken,
                refreshToken: refreshToken,
                expiresIn: 3600,
                tokenType: "Bearer",
                scope: nil
            )
        }
    }
    
    /// 检查是否已登录（优先检查 Keychain）
    func isLoggedIn() -> Bool {
        return KeychainManager.shared.isLoggedIn() || getStoredToken() != nil
    }
    
    /// 更新的退出登录方法
    func logout(completion: @escaping (Bool) -> Void) {
        // 清除 Keychain 中的 OAuth 数据
        KeychainManager.shared.clearOAuthData()
        
        // 清除 UserDefaults 中的数据（向后兼容）
        UserDefaults.standard.removeObject(forKey: "oauth_token")
        UserDefaults.standard.removeObject(forKey: "oauth_refresh_token")
        UserDefaults.standard.removeObject(forKey: "oauth_provider")
        
        // 清除 PKCE 数据
        PKCEManager.shared.clearPKCEData()
        
        // 清除当前会话
        currentAuthSession = nil
        currentOAuthController = nil
        
        completion(true)
    }
}

// MARK: - CVOAuthWebViewControllerDelegate
extension OAuthManager: CVOAuthWebViewControllerDelegate {
    
    func oauthWebViewController(_ controller: CVOAuthWebViewController, didCompleteWithResult result: CVOAuthResult) {
        guard let session = currentAuthSession else { return }
        
        // 关闭 WebView 控制器
        controller.dismiss(animated: true) {
            switch result {
            case .success(let authorizationCode, let state):
                // 验证状态参数
                guard state == session.state else {
                    session.completion(.failure(OAuthError.invalidState))
                    self.currentAuthSession = nil
                    self.currentOAuthController = nil
                    return
                }
                
                // 交换授权码为访问令牌
                self.exchangeCodeForToken(provider: session.provider, authorizationCode: authorizationCode) { tokenResult in
                    session.completion(tokenResult)
                    self.currentAuthSession = nil
                    self.currentOAuthController = nil
                }
                
            case .failure(let error):
                let oauthError: OAuthError
                switch error {
                case .authorizationDenied:
                    oauthError = OAuthError.authorizationDenied
                case .invalidState:
                    oauthError = OAuthError.invalidState
                case .missingAuthorizationCode:
                    oauthError = OAuthError.missingAuthorizationCode
                case .timeout:
                    oauthError = OAuthError.networkError(NSError(domain: "Timeout", code: -1))
                default:
                    oauthError = OAuthError.networkError(NSError(domain: "OAuth Error", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
                }
                
                session.completion(.failure(oauthError))
                self.currentAuthSession = nil
                self.currentOAuthController = nil
                
            case .cancelled:
                session.completion(.failure(OAuthError.authorizationDenied))
                self.currentAuthSession = nil
                self.currentOAuthController = nil
            }
        }
    }
    
    func oauthWebViewController(_ controller: CVOAuthWebViewController, didChangeState state: CVOAuthState) {
        // 可以在这里处理状态变化，比如更新 UI 或记录日志
        #if DEBUG
        print("OAuth State Changed: \(state)")
        #endif
    }