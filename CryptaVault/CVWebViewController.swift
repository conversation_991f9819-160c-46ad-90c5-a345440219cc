//
//  CVWebViewController.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/5.
//

import UIKit
import WebKit
import SnapKit

/// WebView 错误类型
enum CVWebViewError: Error, LocalizedError {
    case loadingFailed(Error)
    case navigationFailed(Error)
    case javascriptExecutionFailed(Error)
    case invalidURL
    case networkError
    case timeout
    
    var errorDescription: String? {
        switch self {
        case .loadingFailed(let error):
            return "页面加载失败: \(error.localizedDescription)"
        case .navigationFailed(let error):
            return "导航失败: \(error.localizedDescription)"
        case .javascriptExecutionFailed(let error):
            return "JavaScript 执行失败: \(error.localizedDescription)"
        case .invalidURL:
            return "无效的 URL"
        case .networkError:
            return "网络错误"
        case .timeout:
            return "加载超时"
        }
    }
}

/// WebView 加载状态
enum CVWebViewLoadingState {
    case idle
    case loading
    case loaded
    case failed(Error)
}

/// WebView 代理协议
protocol CVWebViewControllerDelegate: AnyObject {
    func webViewController(_ controller: CVWebViewController, didStartLoading url: URL?)
    func webViewController(_ controller: CVWebViewController, didFinishLoading url: URL?)
    func webViewController(_ controller: CVWebViewController, didFailLoading error: CVWebViewError)
    func webViewController(_ controller: CVWebViewController, decidePolicyFor navigationAction: WKNavigationAction) -> WKNavigationActionPolicy
    func webViewController(_ controller: CVWebViewController, didReceiveJavaScriptMessage message: WKScriptMessage)
}

/// WebView 代理协议默认实现
extension CVWebViewControllerDelegate {
    func webViewController(_ controller: CVWebViewController, didStartLoading url: URL?) {}
    func webViewController(_ controller: CVWebViewController, didFinishLoading url: URL?) {}
    func webViewController(_ controller: CVWebViewController, didFailLoading error: CVWebViewError) {}
    func webViewController(_ controller: CVWebViewController, decidePolicyFor navigationAction: WKNavigationAction) -> WKNavigationActionPolicy {
        return .allow
    }
    func webViewController(_ controller: CVWebViewController, didReceiveJavaScriptMessage message: WKScriptMessage) {}
}

/// 通用 WebView 视图控制器
/// 提供完整的 WKWebView 功能，包括导航、进度指示、错误处理等
class CVWebViewController: UIViewController {
    
    // MARK: - Properties
    
    /// WebView 代理
    weak var delegate: CVWebViewControllerDelegate?
    
    /// 当前加载状态
    private(set) var loadingState: CVWebViewLoadingState = .idle
    
    /// 是否显示导航工具栏
    var showsNavigationToolbar: Bool = true {
        didSet {
            setupNavigationToolbar()
        }
    }
    
    /// 是否显示进度条
    var showsProgressView: Bool = true {
        didSet {
            setupProgressView()
        }
    }
    
    /// 自定义 User-Agent
    var customUserAgent: String? {
        didSet {
            webView.customUserAgent = customUserAgent
        }
    }
    
    /// 是否允许缩放
    var allowsZoom: Bool = false {
        didSet {
            updateZoomSettings()
        }
    }
    
    // MARK: - UI Components
    
    /// 主要的 WebView
    private(set) lazy var webView: WKWebView = {
        let configuration = WKWebViewConfiguration()
        
        // 配置安全设置
        configuration.websiteDataStore = .nonPersistent() // 默认使用非持久化存储
        configuration.suppressesIncrementalRendering = false
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        // 设置 JavaScript 消息处理器
        let userContentController = WKUserContentController()
        configuration.userContentController = userContentController
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = self
        webView.uiDelegate = self
        webView.allowsBackForwardNavigationGestures = true
        webView.scrollView.showsVerticalScrollIndicator = true
        webView.scrollView.showsHorizontalScrollIndicator = true
        
        return webView
    }()
    
    /// 进度条
    private lazy var progressView: UIProgressView = {
        let progressView = UIProgressView(progressViewStyle: .default)
        progressView.progressTintColor = .systemBlue
        progressView.trackTintColor = .systemGray4
        progressView.isHidden = true
        return progressView
    }()
    
    /// 导航工具栏
    private lazy var navigationToolbar: UIToolbar = {
        let toolbar = UIToolbar()
        toolbar.isTranslucent = true
        toolbar.tintColor = .systemBlue
        return toolbar
    }()
    
    /// 工具栏按钮
    private lazy var backButton: UIBarButtonItem = {
        UIBarButtonItem(image: UIImage(systemName: "chevron.left"), style: .plain, target: self, action: #selector(goBack))
    }()
    
    private lazy var forwardButton: UIBarButtonItem = {
        UIBarButtonItem(image: UIImage(systemName: "chevron.right"), style: .plain, target: self, action: #selector(goForward))
    }()
    
    private lazy var refreshButton: UIBarButtonItem = {
        UIBarButtonItem(image: UIImage(systemName: "arrow.clockwise"), style: .plain, target: self, action: #selector(refresh))
    }()
    
    private lazy var stopButton: UIBarButtonItem = {
        UIBarButtonItem(image: UIImage(systemName: "xmark"), style: .plain, target: self, action: #selector(stopLoading))
    }()
    
    private lazy var closeButton: UIBarButtonItem = {
        UIBarButtonItem(image: UIImage(systemName: "xmark.circle"), style: .plain, target: self, action: #selector(closeWebView))
    }()
    
    private lazy var flexibleSpace: UIBarButtonItem = {
        UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
    }()
    
    /// 错误视图
    private lazy var errorView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.isHidden = true
        
        let imageView = UIImageView(image: UIImage(systemName: "exclamationmark.triangle"))
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemOrange
        
        let titleLabel = UILabel()
        titleLabel.text = "加载失败"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textAlignment = .center
        titleLabel.textColor = .label
        
        let messageLabel = UILabel()
        messageLabel.text = "请检查网络连接或稍后重试"
        messageLabel.font = UIFont.systemFont(ofSize: 14)
        messageLabel.textAlignment = .center
        messageLabel.textColor = .secondaryLabel
        messageLabel.numberOfLines = 0
        
        let retryButton = UIButton(type: .system)
        retryButton.setTitle("重试", for: .normal)
        retryButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        retryButton.backgroundColor = .systemBlue
        retryButton.setTitleColor(.white, for: .normal)
        retryButton.layer.cornerRadius = 8
        retryButton.addTarget(self, action: #selector(retryLoading), for: .touchUpInside)
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(messageLabel)
        view.addSubview(retryButton)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-60)
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        messageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        retryButton.snp.makeConstraints { make in
            make.top.equalTo(messageLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(44)
        }
        
        self.errorRetryButton = retryButton
        self.errorMessageLabel = messageLabel
        
        return view
    }()
    
    private var errorRetryButton: UIButton!
    private var errorMessageLabel: UILabel!
    
    /// 当前加载的 URL
    private var currentURL: URL?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupWebViewObservers()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateNavigationButtons()
    }
    
    deinit {
        webView.removeObserver(self, forKeyPath: "estimatedProgress")
        webView.removeObserver(self, forKeyPath: "canGoBack")
        webView.removeObserver(self, forKeyPath: "canGoForward")
        webView.removeObserver(self, forKeyPath: "isLoading")
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        view.addSubview(webView)
        view.addSubview(progressView)
        view.addSubview(navigationToolbar)
        view.addSubview(errorView)
        
        setupConstraints()
        setupNavigationToolbar()
        setupProgressView()
    }
    
    private func setupConstraints() {
        progressView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(2)
        }
        
        webView.snp.makeConstraints { make in
            make.top.equalTo(progressView.snp.bottom)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(navigationToolbar.snp.top)
        }
        
        navigationToolbar.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide)
            make.height.equalTo(44)
        }
        
        errorView.snp.makeConstraints { make in
            make.edges.equalTo(webView)
        }
    }
    
    private func setupNavigationToolbar() {
        if showsNavigationToolbar {
            navigationToolbar.isHidden = false
            updateNavigationButtons()
        } else {
            navigationToolbar.isHidden = true
            webView.snp.remakeConstraints { make in
                make.top.equalTo(progressView.snp.bottom)
                make.leading.trailing.equalToSuperview()
                make.bottom.equalTo(view.safeAreaLayoutGuide)
            }
        }
    }
    
    private func setupProgressView() {
        progressView.isHidden = !showsProgressView
    }
    
    private func setupWebViewObservers() {
        webView.addObserver(self, forKeyPath: "estimatedProgress", options: .new, context: nil)
        webView.addObserver(self, forKeyPath: "canGoBack", options: .new, context: nil)
        webView.addObserver(self, forKeyPath: "canGoForward", options: .new, context: nil)
        webView.addObserver(self, forKeyPath: "isLoading", options: .new, context: nil)
    }
    
    private func updateZoomSettings() {
        let metaTag = allowsZoom 
            ? "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=yes'>"
            : "<meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=no'>"
        
        let userScript = WKUserScript(source: """
            var meta = document.createElement('meta');
            meta.name = 'viewport';
            meta.content = 'width=device-width, initial-scale=1.0, user-scalable=\(allowsZoom ? "yes" : "no")';
            document.getElementsByTagName('head')[0].appendChild(meta);
        """, injectionTime: .atDocumentEnd, forMainFrameOnly: true)
        
        webView.configuration.userContentController.addUserScript(userScript)
    }
    
    // MARK: - Public Methods
    
    /// 加载 URL
    /// - Parameter url: 要加载的 URL
    func load(url: URL) {
        currentURL = url
        hideErrorView()
        let request = URLRequest(url: url)
        webView.load(request)
    }
    
    /// 加载 URL 字符串
    /// - Parameter urlString: URL 字符串
    func load(urlString: String) {
        guard let url = URL(string: urlString) else {
            showError(.invalidURL)
            return
        }
        load(url: url)
    }
    
    /// 加载 HTML 字符串
    /// - Parameters:
    ///   - htmlString: HTML 内容
    ///   - baseURL: 基础 URL
    func loadHTML(string htmlString: String, baseURL: URL? = nil) {
        hideErrorView()
        webView.loadHTMLString(htmlString, baseURL: baseURL)
    }
    
    /// 执行 JavaScript
    /// - Parameters:
    ///   - script: JavaScript 代码
    ///   - completion: 完成回调
    func evaluateJavaScript(_ script: String, completion: ((Result<Any?, CVWebViewError>) -> Void)? = nil) {
        webView.evaluateJavaScript(script) { result, error in
            if let error = error {
                completion?(.failure(.javascriptExecutionFailed(error)))
            } else {
                completion?(.success(result))
            }
        }
    }
    
    /// 添加 JavaScript 消息处理器
    /// - Parameter name: 消息处理器名称
    func addJavaScriptMessageHandler(name: String) {
        webView.configuration.userContentController.add(self, name: name)
    }
    
    /// 移除 JavaScript 消息处理器
    /// - Parameter name: 消息处理器名称
    func removeJavaScriptMessageHandler(name: String) {
        webView.configuration.userContentController.removeScriptMessageHandler(forName: name)
    }
    
    /// 设置网站数据存储
    /// - Parameter dataStore: 数据存储对象
    func setWebsiteDataStore(_ dataStore: WKWebsiteDataStore) {
        // 注意：这需要重新创建 WebView
        let configuration = webView.configuration
        configuration.websiteDataStore = dataStore
        
        // 重新创建 WebView（如果需要的话）
        // 在实际使用中，应该在初始化时设置
    }
    
    // MARK: - Private Methods
    
    private func updateNavigationButtons() {
        let isLoading = webView.isLoading
        
        backButton.isEnabled = webView.canGoBack
        forwardButton.isEnabled = webView.canGoForward
        
        let actionButton = isLoading ? stopButton : refreshButton
        
        navigationToolbar.items = [
            backButton,
            flexibleSpace,
            forwardButton,
            flexibleSpace,
            actionButton,
            flexibleSpace,
            closeButton
        ]
    }
    
    private func showError(_ error: CVWebViewError) {
        loadingState = .failed(error)
        errorMessageLabel.text = error.localizedDescription
        errorView.isHidden = false
        webView.isHidden = true
        delegate?.webViewController(self, didFailLoading: error)
    }
    
    private func hideErrorView() {
        errorView.isHidden = true
        webView.isHidden = false
    }
    
    // MARK: - Action Methods
    
    @objc private func goBack() {
        webView.goBack()
    }
    
    @objc private func goForward() {
        webView.goForward()
    }
    
    @objc private func refresh() {
        webView.reload()
    }
    
    @objc private func stopLoading() {
        webView.stopLoading()
    }
    
    @objc private func closeWebView() {
        dismiss(animated: true)
    }
    
    @objc private func retryLoading() {
        guard let url = currentURL else { return }
        load(url: url)
    }
    
    // MARK: - KVO
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        switch keyPath {
        case "estimatedProgress":
            DispatchQueue.main.async {
                self.progressView.progress = Float(self.webView.estimatedProgress)
            }
            
        case "canGoBack", "canGoForward", "isLoading":
            DispatchQueue.main.async {
                self.updateNavigationButtons()
            }
            
        default:
            super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
        }
    }
}

// MARK: - WKNavigationDelegate
extension CVWebViewController: WKNavigationDelegate {
    
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        loadingState = .loading
        progressView.isHidden = false
        hideErrorView()
        delegate?.webViewController(self, didStartLoading: webView.url)
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        loadingState = .loaded
        progressView.isHidden = true
        delegate?.webViewController(self, didFinishLoading: webView.url)
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        progressView.isHidden = true
        showError(.loadingFailed(error))
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        progressView.isHidden = true
        showError(.loadingFailed(error))
    }
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        let policy = delegate?.webViewController(self, decidePolicyFor: navigationAction) ?? .allow
        decisionHandler(policy)
    }
}

// MARK: - WKUIDelegate
extension CVWebViewController: WKUIDelegate {
    
    func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {
        // 处理在新窗口中打开的链接
        if navigationAction.targetFrame == nil {
            webView.load(navigationAction.request)
        }
        return nil
    }
    
    func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            completionHandler()
        })
        present(alert, animated: true)
    }
    
    func webView(_ webView: WKWebView, runJavaScriptConfirmPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (Bool) -> Void) {
        let alert = UIAlertController(title: "确认", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
            completionHandler(false)
        })
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            completionHandler(true)
        })
        present(alert, animated: true)
    }
}

// MARK: - WKScriptMessageHandler
extension CVWebViewController: WKScriptMessageHandler {
    
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        delegate?.webViewController(self, didReceiveJavaScriptMessage: message)
    }
}