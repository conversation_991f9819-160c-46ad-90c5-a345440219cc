//
//  PKCEManager.swift
//  CryptaVault
//
//  Created by <PERSON><PERSON><PERSON> on 2025/8/5.
//

import Foundation
import CryptoKit

/// PKCE (Proof Key for Code Exchange) 管理器
/// 实现 RFC 7636 标准，用于增强 OAuth 2.0 安全性
class PKCEManager {
    
    /// PKCE 错误类型
    enum PKCEError: Error, LocalizedError {
        case codeVerifierGenerationFailed
        case codeChallengeGenerationFailed
        case invalidCodeVerifier
        
        var errorDescription: String? {
            switch self {
            case .codeVerifierGenerationFailed:
                return "Failed to generate PKCE code verifier"
            case .codeChallengeGenerationFailed:
                return "Failed to generate PKCE code challenge"
            case .invalidCodeVerifier:
                return "Invalid PKCE code verifier format"
            }
        }
    }
    
    /// Code Challenge 方法
    enum CodeChallengeMethod: String {
        case sha256 = "S256"
        case plain = "plain"
    }
    
    /// PKCE 参数结构
    struct PKCEParams {
        let codeVerifier: String
        let codeChallenge: String
        let codeChallengeMethod: CodeChallengeMethod
    }
    
    static let shared = PKCEManager()
    
    private init() {}
    
    // MARK: - PKCE 生成方法
    
    /// 生成 PKCE 参数
    /// - Parameter method: Code Challenge 方法，默认为 SHA256
    /// - Returns: PKCE 参数
    /// - Throws: PKCEError
    func generatePKCEParams(method: CodeChallengeMethod = .sha256) throws -> PKCEParams {
        let codeVerifier = try generateCodeVerifier()
        let codeChallenge = try generateCodeChallenge(from: codeVerifier, method: method)
        
        return PKCEParams(
            codeVerifier: codeVerifier,
            codeChallenge: codeChallenge,
            codeChallengeMethod: method
        )
    }
    
    /// 生成 Code Verifier
    /// 根据 RFC 7636 规范：
    /// - 长度：43-128 个字符
    /// - 字符集：[A-Z] / [a-z] / [0-9] / "-" / "." / "_" / "~"
    /// - Returns: Code Verifier 字符串
    /// - Throws: PKCEError
    private func generateCodeVerifier() throws -> String {
        let allowedCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~"
        let codeVerifierLength = 128 // 使用最大长度确保安全性
        
        var codeVerifier = ""
        
        for _ in 0..<codeVerifierLength {
            guard let randomCharacter = allowedCharacters.randomElement() else {
                throw PKCEError.codeVerifierGenerationFailed
            }
            codeVerifier.append(randomCharacter)
        }
        
        guard isValidCodeVerifier(codeVerifier) else {
            throw PKCEError.codeVerifierGenerationFailed
        }
        
        return codeVerifier
    }
    
    /// 生成 Code Challenge
    /// - Parameters:
    ///   - codeVerifier: Code Verifier
    ///   - method: 生成方法
    /// - Returns: Code Challenge 字符串
    /// - Throws: PKCEError
    private func generateCodeChallenge(from codeVerifier: String, method: CodeChallengeMethod) throws -> String {
        switch method {
        case .plain:
            return codeVerifier
            
        case .sha256:
            guard let data = codeVerifier.data(using: .utf8) else {
                throw PKCEError.codeChallengeGenerationFailed
            }
            
            let digest = SHA256.hash(data: data)
            return Data(digest).base64URLEncodedString()
        }
    }
    
    /// 验证 Code Verifier 格式
    /// - Parameter codeVerifier: Code Verifier
    /// - Returns: 是否有效
    private func isValidCodeVerifier(_ codeVerifier: String) -> Bool {
        // 检查长度 (43-128 字符)
        guard codeVerifier.count >= 43 && codeVerifier.count <= 128 else {
            return false
        }
        
        // 检查字符集
        let allowedCharacterSet = CharacterSet(charactersIn: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~")
        let codeVerifierCharacterSet = CharacterSet(charactersIn: codeVerifier)
        
        return allowedCharacterSet.isSuperset(of: codeVerifierCharacterSet)
    }
    
    // MARK: - 验证方法
    
    /// 验证 Code Challenge
    /// - Parameters:
    ///   - codeVerifier: 原始 Code Verifier
    ///   - codeChallenge: 要验证的 Code Challenge
    ///   - method: 生成方法
    /// - Returns: 是否匹配
    func verifyCodeChallenge(codeVerifier: String, codeChallenge: String, method: CodeChallengeMethod) -> Bool {
        do {
            let expectedCodeChallenge = try generateCodeChallenge(from: codeVerifier, method: method)
            return expectedCodeChallenge == codeChallenge
        } catch {
            return false
        }
    }
    
    // MARK: - 安全存储集成
    
    /// 生成并安全存储 PKCE 参数
    /// - Parameter method: Code Challenge 方法
    /// - Returns: Code Challenge 和方法（用于 OAuth 请求）
    /// - Throws: PKCEError, KeychainError
    func generateAndStorePKCEParams(method: CodeChallengeMethod = .sha256) throws -> (challenge: String, method: CodeChallengeMethod) {
        let pkceParams = try generatePKCEParams(method: method)
        
        // 安全存储 Code Verifier 到 Keychain
        try KeychainManager.shared.storeCodeVerifier(pkceParams.codeVerifier)
        
        return (challenge: pkceParams.codeChallenge, method: pkceParams.codeChallengeMethod)
    }
    
    /// 从 Keychain 获取 Code Verifier 并清理
    /// - Returns: Code Verifier
    /// - Throws: KeychainError
    func getAndClearCodeVerifier() throws -> String {
        let codeVerifier = try KeychainManager.shared.getCodeVerifier()
        try KeychainManager.shared.delete(forKey: KeychainManager.OAuthKeys.codeVerifier)
        return codeVerifier
    }
    
    /// 清理存储的 PKCE 数据
    func clearPKCEData() {
        try? KeychainManager.shared.delete(forKey: KeychainManager.OAuthKeys.codeVerifier)
    }
}

// MARK: - Data Extension for Base64URL
extension Data {
    
    /// Base64URL 编码（无填充）
    /// 符合 RFC 4648 Section 5 规范
    /// - Returns: Base64URL 编码字符串
    func base64URLEncodedString() -> String {
        return base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "=", with: "")
    }
    
    /// 从 Base64URL 字符串初始化 Data
    /// - Parameter base64URLString: Base64URL 编码字符串
    init?(base64URLEncoded base64URLString: String) {
        var base64String = base64URLString
            .replacingOccurrences(of: "-", with: "+")
            .replacingOccurrences(of: "_", with: "/")
        
        // 添加必要的填充
        let padding = base64String.count % 4
        if padding > 0 {
            base64String += String(repeating: "=", count: 4 - padding)
        }
        
        self.init(base64Encoded: base64String)
    }
}

// MARK: - 测试和调试支持
extension PKCEManager {
    
    /// 生成测试用的 PKCE 参数（仅用于调试）
    /// - Warning: 仅用于开发和测试，不应在生产环境中使用
    /// - Returns: 固定的 PKCE 参数
    func generateTestPKCEParams() -> PKCEParams {
        let testCodeVerifier = "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk"
        let testCodeChallenge = "E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM"
        
        return PKCEParams(
            codeVerifier: testCodeVerifier,
            codeChallenge: testCodeChallenge,
            codeChallengeMethod: .sha256
        )
    }
    
    /// 打印 PKCE 参数信息（仅用于调试）
    /// - Parameter params: PKCE 参数
    func debugPrintPKCEParams(_ params: PKCEParams) {
        #if DEBUG
        print("=== PKCE Parameters ===")
        print("Code Verifier: \(params.codeVerifier)")
        print("Code Challenge: \(params.codeChallenge)")
        print("Challenge Method: \(params.codeChallengeMethod.rawValue)")
        print("Verifier Length: \(params.codeVerifier.count)")
        print("Challenge Length: \(params.codeChallenge.count)")
        print("=====================")
        #endif
    }
}